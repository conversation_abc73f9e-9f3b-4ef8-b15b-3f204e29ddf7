"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { use, useCallback } from "react";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "noBatch",
    label: "No Batch",
  },
  {
    key: "tglMasuk",
    label: "Tanggal Masuk",
  },
  {
    key: "jumlahMasuk",
    label: "Jumlah Masuk",
  },
  {
    key: "sisa",
    label: "Sisa",  
  },
    {
    key: "tglKadaluwarsa",
    label: "Tanggal Kadaluarsa",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TablePencatatan() {
  const result: any[] = []


  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "active":
          return (
            <div className="flex gap-2 justify-end items-center ">
            
            </div>
          );
        case "action":
          return 
        default:
          return category[column_key];
      }
    },
    []
  );

    const props: PropsTable<any> = {columns,renderCell, data: result, basePath:"/referensi/poli", parameter:true, isTab:true }
  return (
    <div className=" rounded-md flex flex-col gap-6 pt-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium lg:text-xl">Daftar Pencatatan Obat</h1>
        <ButtonNavigation href={`/obat/inventori-obat/detail/1/add`} tema="light" icon="plus" tooltip="Tambah Data" title="Obat Masuk" />
      </div>
      < TablePoliklinik {...props} />
    </div>
  );
}

