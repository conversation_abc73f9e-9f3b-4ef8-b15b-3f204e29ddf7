import { Button, SelectItem } from "@heroui/react";
import React from "react";
import { data } from "./form-header";
import { FormSelect } from "@/components/custom/select";
import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { Plus, Trash2 } from "lucide-react";
import { items } from "./form-add-stuff";
import { FormikProps } from "formik";
import { DateValue } from "@internationalized/date";
import { currencyFormatter } from "@/libs/currency-formatter";

type payload = {
  title: string;
  typeDrug: string;
  type: string;
  fund: string;
  receipt: string;
  date: DateValue | null;
  items: Array<items>;
};

const FormDinamic = React.memo(
  function FormDinamic({
    formik,
    addItem,
    removeItem,
  }: {
    formik: FormikProps<payload>;
    addItem: () => void;
    removeItem: (index: number) => void;
  }) {
    const total = formik.values.items.map(
      (item) => (item?.assetValue ?? 0) * (item?.quantity ?? 0)
    );

    const quantity = formik.values.items
      .filter((item) => item?.quantity)
      .reduce((acc, item) => acc + (item?.quantity ?? 0), 0);

    return (
      <div className="flex flex-col gap-4">
        <div className=""></div>
        {/* List of Obat Items */}
        <div className="flex flex-col gap-4">
          {formik.values.items.map((obat: any, index: number) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg flex flex-col gap-6 p-4 bg-default-50"
            >
              <div className="flex items-center justify-between">
                <h3 className="font-medium ">Barang {index + 1}</h3>
                <Button
                  type="button"
                  color="danger"
                  variant="solid"
                  size="sm"
                  isIconOnly
                  onPress={() => removeItem(index)}
                >
                  <Trash2 size={16} />
                </Button>
              </div>

              {/* Nama Obat */}
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="tglMasuk" className="text-sm">
                  Nama Barang
                </label>
                <FormInput
                  name={`items.${index}.name`}
                  formik={formik}
                  placeholder="Ketik nama"
                />
              </div>

              <div className="grid  grid-cols-1  lg:grid-cols-3 gap-4">
                <div className="flex flex-col gap-1 w-full">
                  <label htmlFor="tglMasuk" className="text-sm">
                    Kuantitas
                  </label>
                  <FormInput
                    name={`items.${index}.quantity`}
                    isNumeric={true}
                    formik={formik}
                    placeholder="0"
                  />
                </div>
                <div className="flex flex-col gap-1 w-full">
                  <label htmlFor="tglMasuk" className="text-sm">
                    Nilai Aset Satuan(Rp)
                  </label>
                  <FormInput
                    debounceMs={800}
                    isClearable={true}
                    name={`items.${index}.assetValue`}
                    isNumeric={true}
                    formik={formik}
                    placeholder="0"
                  />
                </div>
                <div className="flex flex-col gap-1 w-full">
                  <label htmlFor="tglMasuk" className="text-sm">
                    Tipe Barang
                  </label>
                  <FormSelect
                    showSeachbar={true}
                    name={`items.${index}.typeFund`}
                    formik={formik}
                    placeholder="Pilih tipe aset"
                    value={obat.typeFund}
                  >
                    {data.map((item) => (
                      <SelectItem
                        key={item.id}
                        textValue={item.nameObat}
                        // Menyimpan seluruh data item sebagai prop
                      >
                        <p>{item.nameObat}</p>
                      </SelectItem>
                    ))}
                  </FormSelect>
                </div>
              </div>
              <div className="grid  grid-cols-1  lg:grid-cols-3 gap-4">
                <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                  <label className="text-sm">Tanggal Perolehan</label>
                  <FormDatePicker
                    name={`items.${index}.dateAcquired`}
                    formik={formik}
                  />
                </div>
                <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                  <label className="text-sm">Tanggal Kadaluarsa</label>
                  <FormDatePicker
                    name={`items.${index}.dateExpired`}
                    formik={formik}
                  />
                </div>
                <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                  <label className="text-sm">Lokasi (penyimpanan barang)</label>
                  <FormSelect
                    showSeachbar={true}
                    name={`items.${index}.location`}
                    formik={formik}
                    placeholder="Pilih ruangan"
                  >
                    {data.map((item) => (
                      <SelectItem
                        key={item.id}
                        textValue={item.nameObat}
                        // Menyimpan seluruh data item sebagai prop
                      >
                        <p>{item.nameObat}</p>
                      </SelectItem>
                    ))}
                  </FormSelect>
                </div>
              </div>

              {/* Tanggal Expired */}
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between">
          <Button
            type="button"
            color="default"
            className="bg-success-100 font-semibold text-green-600"
            size="sm"
            startContent={<Plus size={16} />}
            onPress={addItem}
          >
            Tambah Barang
          </Button>
        </div>
        <span className=" border-b-2 border-default-400 pb-2 " />
        {/* Submit Button */}
        <div className="flex gap-6 justify-end pt-4">
          <div className=" lg:w-1/3 md:w-1/3 flex gap-6 ">
            <div className="">
              <p className=" text-sm mb-2">Total Aset</p>
              <p className=" text-sm mb-2">Total Nilai Aset</p>
            </div>
            <div className="">
              <p className=" text-sm mb-2">: {quantity}</p>
              <p className=" text-sm mb-2">
                : Rp. {currencyFormatter(total[0], ".", false, "")}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if items array changes or functions change
    if (prevProps.formik.values.items !== nextProps.formik.values.items) {
      return false; // Re-render
    }

    // Check if any item-related errors changed
    if (
      JSON.stringify(prevProps.formik.errors.items) !==
      JSON.stringify(nextProps.formik.errors.items)
    ) {
      return false; // Re-render
    }

    // Check if any item-related touched changed
    if (
      JSON.stringify(prevProps.formik.touched.items) !==
      JSON.stringify(nextProps.formik.touched.items)
    ) {
      return false; // Re-render
    }

    return true; // Don't re-render
  }
);

export default FormDinamic;
