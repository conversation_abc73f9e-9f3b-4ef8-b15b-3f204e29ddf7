import { Button, SelectItem } from "@heroui/react";
import React from "react";
import { data } from "./form-header";
import { FormSelect } from "@/components/custom/select";
import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { Plus, Trash2 } from "lucide-react";
import { items } from "./form-add-stuff";
import { FormikProps } from "formik";
import { DateValue } from "@internationalized/date";

type payload = {
  title: string;
  typeDrug: string;
  type: string;
  receipt: number;
  date: DateValue | null;
  items: Array<items>;
};

export default function FormDinamic({
  formik,
  addItem,
  removeItem,
  updateItem,
}: {
  formik: FormikProps<payload>;
  addItem: () => void;
  removeItem: (index: number) => void;
  updateItem: (index: number, field: keyof items, value: any) => void;
}) {
  return (
    <div className="flex flex-col gap-4">
      <div className=""></div>
      {/* List of Obat Items */}
      <div className="flex flex-col gap-4">
        {formik.values.items.map((obat: any, index: number) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg flex flex-col gap-6 p-4 bg-default-50"
          >
            <div className="flex items-center justify-between">
              <h3 className="font-medium ">Barang {index + 1}</h3>
              <Button
                type="button"
                color="danger"
                variant="solid"
                size="sm"
                isIconOnly
                onClick={() => removeItem(index)}
              >
                <Trash2 size={16} />
              </Button>
            </div>

            {/* Nama Obat */}
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                Nama Barang
              </label>
              <FormInput
                name={`items.${index}.name`}
                isClearable={false}
                formik={formik}
                placeholder="Ketik nama"
              />
            </div>

            <div className="grid  grid-cols-1  lg:grid-cols-3 gap-4">
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="tglMasuk" className="text-sm">
                  Kuantitas
                </label>
                <FormInput
                  isClearable={false}
                  name={`items.${index}.quantity`}
                  isNumeric={true}
                  formik={formik}
                  placeholder="0"
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="tglMasuk" className="text-sm">
                  Nilai Aset Satuan(Rp)
                </label>
                <FormInput
                  isClearable={false}
                  name={`items.${index}.assetValue`}
                  isNumeric={true}
                  formik={formik}
                  placeholder="0"
                />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="tglMasuk" className="text-sm">
                  Tipe Barang
                </label>
                <FormSelect
                  showSeachbar={true}
                  name={`items.${index}.typeFund`}
                  formik={formik}
                  placeholder="Pilih tipe aset"
                  value={obat.typeFund}
                >
                  {data.map((item) => (
                    <SelectItem
                      key={item.id}
                      textValue={item.nameObat}
                      // Menyimpan seluruh data item sebagai prop
                    >
                      <p>{item.nameObat}</p>
                    </SelectItem>
                  ))}
                </FormSelect>
              </div>
            </div>
            <div className="grid  grid-cols-1  lg:grid-cols-3 gap-4">
              <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                <label className="text-sm">Tanggal Perolehan</label>
                <FormDatePicker
                  name={`items.${index}.dateAcquired`}
                  formik={formik}
                />
              </div>
              <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                <label className="text-sm">Tanggal Kadaluarsa</label>
                <FormDatePicker
                  name={`items.${index}.dateExpired`}
                  formik={formik}
                />
              </div>
              <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                <label className="text-sm">Lokasi (penyimpanan barang)</label>
                <FormSelect
                  showSeachbar={true}
                  name={`items.${index}.location`}
                  formik={formik}
                  placeholder="Pilih ruangan"
                >
                  {data.map((item) => (
                    <SelectItem
                      key={item.id}
                      textValue={item.nameObat}
                      // Menyimpan seluruh data item sebagai prop
                    >
                      <p>{item.nameObat}</p>
                    </SelectItem>
                  ))}
                </FormSelect>
              </div>
            </div>

            {/* Tanggal Expired */}
          </div>
        ))}
      </div>
      <div className="flex items-center justify-between">
        <Button
          type="button"
          color="default"
          className="bg-success-100 font-semibold text-green-600"
          size="sm"
          startContent={<Plus size={16} />}
          onPress={addItem}
        >
          Tambah Barang
        </Button>
      </div>
    </div>
  );
}
