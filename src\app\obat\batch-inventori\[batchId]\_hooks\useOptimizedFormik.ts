import { useFormik } from "formik";
import { useCallback, useMemo } from "react";
import { DateValue } from "@internationalized/date";

export type items = {
  id: string;
  name: string;
  quantity: number;
  assetValue: number;
  typeFund: string;
  dateAcquired: DateValue | null;
  dateExpired: DateValue | null;
  location: string;
};

type payload = {
  title: string;
  typeDrug: string;
  type: string;
  fund: string;
  receipt: string;
  date: DateValue | null;
  items: Array<items>;
};

export function useOptimizedFormik(
  initialValues: payload,
  validationSchema: any,
  onSubmit: (values: payload) => void
) {
  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema,
    onSubmit,
  });

  // Memoize header values to prevent unnecessary re-renders
  const headerValues = useMemo(() => ({
    title: formik.values.title,
    typeDrug: formik.values.typeDrug,
    type: formik.values.type,
    fund: formik.values.fund,
    receipt: formik.values.receipt,
    date: formik.values.date,
  }), [
    formik.values.title,
    formik.values.typeDrug,
    formik.values.type,
    formik.values.fund,
    formik.values.receipt,
    formik.values.date,
  ]);

  // Memoize items values to prevent unnecessary re-renders
  const itemsValues = useMemo(() => formik.values.items, [formik.values.items]);

  // Optimized field setters
  const setHeaderField = useCallback((field: keyof typeof headerValues, value: any) => {
    formik.setFieldValue(field, value);
  }, [formik]);

  const setItemField = useCallback((index: number, field: keyof items, value: any) => {
    formik.setFieldValue(`items.${index}.${field}`, value);
  }, [formik]);

  // Optimized array operations
  const addItem = useCallback(() => {
    const newItem: items = {
      id: `obat-${Date.now()}`,
      name: "",
      quantity: 0,
      assetValue: 0,
      typeFund: "",
      dateAcquired: null,
      dateExpired: null,
      location: "",
    };
    formik.setFieldValue("items", [...formik.values.items, newItem]);
  }, [formik]);

  const removeItem = useCallback((index: number) => {
    const updatedList = formik.values.items.filter((_, i) => i !== index);
    formik.setFieldValue("items", updatedList);
  }, [formik]);

  return {
    formik,
    headerValues,
    itemsValues,
    setHeaderField,
    setItemField,
    addItem,
    removeItem,
  };
}
