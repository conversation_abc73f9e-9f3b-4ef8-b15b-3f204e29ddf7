"use client";

import UTable from "@/components/custom/table";
import {
    <PERSON>ner,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow
} from "@heroui/react";
import { useCallback } from "react";

const data = [
    {id:"1", jenis : "example", nilai: "2.3", satuan: "%"},
    {id:"2", jenis : "example", nilai: "2.3", satuan: "%"},
    {id:"3", jenis : "example", nilai: "2.3", satuan: "%"},
    {id:"4", jenis : "example", nilai: "2.3", satuan: "%"},
]

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "jenis",
    label: "<PERSON><PERSON>em<PERSON>",
  },
  {
    key: "nilai",
    label: "Nilai Rujukan",
  },
  {
    key: "satuan",
    label: "Satuan",
  },
];


export default function TableDetailLayanan() {

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "jenis":
          return <div className="lg:w-[300px]">{category.jenis}</div>;
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className=" flex flex-col gap-2">
      <div className="flex justify-between items-center">
        <h1 className="">Daftar Lengkap</h1>
      </div>
     <div >
       <UTable  bordered={true} parameter={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
            className="text-default-800"
              align={
                column.key === "action" || column.key === "active"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data ?? []}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {data?.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
     </div>
    </div>
  );
}
