'use client'

import { But<PERSON>, Toolt<PERSON> } from "@heroui/react"
import { Eye, Pencil, Plus } from "lucide-react"
import { useRouter } from "next/navigation"

type Props = {
    tema?: "primary" | "secondary" | "light"
    href: string
    tooltip?: string
    title?: string
    icon?: "pencil" | "eye" | "plus"
    isIcon?:boolean
}

export default function ButtonNavigation({ tema = "primary", href, tooltip, title, icon="plus", isIcon=false }: Props) {
    const router = useRouter()
    const onNavigation = () => router.push(href)
    const choiceButton = () => {
        switch (tema) {
            case "light":
                return (
                    <Tooltip content={tooltip ?? "Tambah Data"}>
                        <Button
                            startContent={icon === "eye" ? <Eye className="w-4" /> : icon === "pencil" ? <Pencil className="w-4" /> : <Plus className="w-4" />}
                            color="default"
                            className="bg-default-50 border-2 rouded-md"
                            size="sm"
                            onPress={onNavigation}
                            isIconOnly={isIcon}
                        >
                            {title}
                        </Button>
                    </Tooltip >
                )
            case "secondary":
                return (
                    <Tooltip content={tooltip ?? "Tambah Data"}>
                        <Button
                            startContent={icon === "eye" ? <Eye className="w-4" /> : icon === "pencil" ? <Pencil className="w-4" /> : <Plus className="w-4" />}
                            color="secondary"
                            size="sm"
                            onPress={onNavigation}
                            isIconOnly={isIcon}
                        >
                            {title}
                        </Button>
                    </Tooltip >
                )
            default:
                return (
                    <Tooltip content={tooltip ?? "Tambah Data"}>
                        <Button
                            startContent={icon === "eye" ? <Eye className="w-4" /> : icon === "pencil" ? <Pencil className="w-4" /> : <Plus className="w-4" />}
                            color="primary"
                            size="sm"
                            onPress={onNavigation}
                            isIconOnly={isIcon}
                        >
                            {title}
                        </Button>
                    </Tooltip >
                )
        }

    }

    return choiceButton()
}