// Utility functions for safe number operations in forms

/**
 * Safely converts a value to number, handling string inputs from form fields
 * @param value - The value to convert (can be string, number, null, undefined)
 * @returns A valid number, defaulting to 0 for invalid inputs
 */
export function safeToNumber(value: any): number {
  if (value === null || value === undefined || value === '') {
    return 0;
  }
  
  // If it's already a number, return it
  if (typeof value === 'number') {
    return isNaN(value) ? 0 : value;
  }
  
  // If it's a string, try to convert
  if (typeof value === 'string') {
    // Remove any formatting (dots, commas, etc.)
    const cleanValue = value.replace(/[^\d.-]/g, '');
    const parsed = Number(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }
  
  // For any other type, try to convert
  const parsed = Number(value);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Safely sums an array of values, ensuring proper numeric addition
 * @param values - Array of values to sum
 * @returns The sum as a number
 */
export function safeSum(values: any[]): number {
  return values.reduce((acc, value) => acc + safeToNumber(value), 0);
}

/**
 * Safely multiplies two values, ensuring proper numeric multiplication
 * @param a - First value
 * @param b - Second value
 * @returns The product as a number
 */
export function safeMultiply(a: any, b: any): number {
  return safeToNumber(a) * safeToNumber(b);
}

/**
 * Formats a number with thousands separator (dots)
 * @param value - The number to format
 * @returns Formatted string with dots as thousands separator
 */
export function formatNumberWithDots(value: any): string {
  const num = safeToNumber(value);
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

/**
 * Parses a formatted number string back to number
 * @param formattedValue - String with dots as thousands separator
 * @returns The parsed number
 */
export function parseFormattedNumber(formattedValue: string): number {
  const cleanValue = formattedValue.replace(/\./g, '');
  return safeToNumber(cleanValue);
}
