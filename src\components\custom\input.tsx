import { Input, InputProps, Textarea, TextAreaProps } from "@heroui/react";
import { KeyboardEvent } from "react";
import { FormikProps, getIn } from "formik";
import { useState, useCallback, useRef, useEffect } from "react";

import { classNames } from "@/constant/constant";
import { NestedKeyOf } from "@/libs/nested-key";

type InputFilterProps<T> = {
  name: keyof T;
  formik: FormikProps<T>;
  submitOnEnter?: boolean;
} & InputProps;

export function FilterInput<T>({
  name,
  formik,
  submitOnEnter,
  isClearable,
  ...res
}: InputFilterProps<T>) {
  const [key, setKey] = useState(new Date().toISOString());
  const isValid = !!(
    formik.touched[name as keyof T] && formik.errors[name as keyof T]
  );

  const handleKeyPress = (
    event: KeyboardEvent<HTMLInputElement> | KeyboardEvent
  ) => {
    if (event.key === "Enter") {
      event.preventDefault();
      formik.validateForm().then(() => formik.submitForm());
    }
  };

  const handleClear = async () => {
    await formik.setFieldValue(name, undefined);
    formik.validateForm().then(() => formik.submitForm());
    setKey(new Date().toISOString());
  };

  return (
    <Input
      key={key}
      classNames={classNames.input}
      color={isValid ? "danger" : "default"}
      enterKeyHint="done"
      errorMessage={formik.errors[name]?.toString()}
      isInvalid={isValid}
      {...formik.getFieldProps(name)}
      value={formik.values?.[name]?.toString()}
      onClear={isClearable ? handleClear : undefined}
      onKeyDown={submitOnEnter ? handleKeyPress : undefined}
      {...res}
    />
  );
}

type FormInputProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  submitOnEnter?: boolean;
  isNumeric?: boolean; // Flag to enable numeric formatting
} & InputProps;

export function FormInput<T>({
  name,
  formik,
  classNames: additionalClassNames,
  isNumeric,
  isClearable,
  debounceMs = 300,
  ...res
}: FormInputProps<T> & { debounceMs?: number }) {
  const [key, setKey] = useState(new Date().toISOString());
  const [localValue, setLocalValue] = useState(() => {
    const formikValue = getIn(formik.values, name);
    return isNumeric
      ? formatNumber(formikValue?.toString() || "")
      : formikValue || "";
  });

  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const isInValid = !!(touched && error);

  // Debounced update to formik
  const debouncedUpdate = useCallback(
    (value: string) => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        if (isNumeric) {
          formik.setFieldValue(name as string, parseNumber(value));
        } else {
          formik.setFieldValue(name as string, value);
        }
      }, debounceMs);
    },
    [formik, name, isNumeric, debounceMs]
  );

  // Update local value when formik value changes externally
  useEffect(() => {
    const formikValue = getIn(formik.values, name);
    const displayValue = isNumeric
      ? formatNumber(formikValue?.toString() || "")
      : formikValue || "";

    if (displayValue !== localValue) {
      setLocalValue(displayValue);
    }
  }, [getIn(formik.values, name), isNumeric]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;

      if (isNumeric) {
        const formattedValue = formatNumber(rawValue);
        setLocalValue(formattedValue);
        debouncedUpdate(formattedValue);
      } else {
        setLocalValue(rawValue);
        debouncedUpdate(rawValue);
      }
    },
    [isNumeric, debouncedUpdate]
  );

  const handleClear = useCallback(async () => {
    setLocalValue("");
    await formik.setFieldValue(name, undefined);
    formik.validateForm().then(() => formik.submitForm());
    setKey(new Date().toISOString());
  }, [formik, name]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <Input
      key={key}
      classNames={{ label: classNames.input.label, ...additionalClassNames }}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      value={localValue}
      variant="bordered"
      onChange={handleChange}
      onClear={isClearable ? handleClear : undefined}
      {...res}
    />
  );
}

// Utility functions
const formatNumber = (num: string) =>
  num.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

const parseNumber = (formattedValue: string) => {
  const cleanValue = formattedValue.replace(/\./g, "");
  return cleanValue === "" ? 0 : Number(cleanValue) || 0;
};

type FormTextAreaProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  submitOnEnter?: boolean;
} & TextAreaProps;

export function FormTextarea<T>({
  name,
  formik,
  isClearable,
  ...res
}: FormTextAreaProps<T>) {
  const [key, setKey] = useState(new Date().toISOString());
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);

  const isInValid = !!(touched && error);

  const handleClear = async () => {
    await formik.setFieldValue(name, undefined);
    formik.validateForm().then(() => formik.submitForm());
    setKey(new Date().toISOString());
  };

  return (
    <Textarea
      key={key}
      classNames={classNames.input}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      onClear={isClearable ? handleClear : undefined}
      {...formik.getFieldProps(name)}
      {...res}
    />
  );
}

type FormUploadProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  submitOnEnter?: boolean;
} & InputProps;

export function FormUpload<T>({
  name,
  formik,
  onChange,
  ...res
}: FormUploadProps<T>) {
  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);

  const isInValid = !!(touched && error);

  return (
    <Input
      classNames={classNames.fileUpload}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      {...res}
      type="file"
      onChange={(e) => {
        const files = e.currentTarget.files?.[0];

        if (files) {
          formik.setFieldValue(name, files);
        }
        if (onChange) onChange(e);
      }}
    />
  );
}
