"use client";

import { But<PERSON> } from "@heroui/react";
import { DateValue } from "@internationalized/date";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { postItem } from "../_schema/post-stuff";
import FormDinamic from "./form-dinamic";
import FormHeader from "./form-header";

export const data = [
  {
    id: "1",
    nameObat: "Amoxilin",
    dosis: "500mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "2",
    nameObat: "Amoxilin",
    dosis: "100mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "3",
    nameObat: "Paracetamol",
    dosis: "1000mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
];

export type items = {
  id: string;
  name: string;
  quantity: number | null;
  assetValue: number | null;
  typeFund: string;
  dateAcquired: DateValue | null;
  dateExpired: DateValue | null;
  location: string;
};

type payload = {
  title: string;
  typeDrug: string;
  type: string;
  fund: string;
  receipt: string;
  date: DateValue | null;
  items: Array<items>;
};

const initialList = {
  id: `obat-${Date.now()}`,
  name: "",
  quantity: null,
  assetValue: null,
  typeFund: "",
  dateAcquired: null,
  dateExpired: null,
  location: "",
};

export default function FormAddStuff() {
  const route = useRouter();
  const [resultFilter, setResultFilter] = useState<(typeof data)[0] | null>(
    null
  );

  const handleSubmit = async (value: payload) => {
    console.log({ value });
  };

  const initialValues: payload = {
    title: "",
    typeDrug: "",
    type: "",
    fund: "",
    receipt: "",
    date: null,
    items: [initialList],
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postItem,
    onSubmit: handleSubmit,
  });

  // Function to add new obat item
  const addObatItem = useCallback(() => {
    const newObat: items = {
      id: `obat-${Date.now()}`,
      name: "",
      quantity: null,
      assetValue: null,
      typeFund: "",
      dateAcquired: null,
      dateExpired: null,
      location: "",
    };

    formik.setFieldValue("items", [...formik.values.items, newObat]);
  }, [formik]);

  // Function to remove obat item
  const removeItem = useCallback(
    (index: number) => {
      if (formik.values.items.length === 1) return;
      const updatedList = formik.values.items.filter((_, i) => i !== index);
      formik.setFieldValue("items", updatedList);
    },
    [formik]
  );

  // Function to update specific obat item
  const updateItem = useCallback(
    (index: number, field: keyof items, value: any) => {
      const updatedList = [...formik.values.items];
      updatedList[index] = { ...updatedList[index], [field]: value };
      formik.setFieldValue("items", updatedList);
      const result = data.find((item) => item.id === value);
      setResultFilter(result ? result : null);
    },
    [formik]
  );

  return (
    <div className=" flex justify-center pb-4 ">
      <div className="bg-default-50 flex flex-col gap-6 p-5 rounded-md  ">
        <h1 className="text-xl font-medium mb-2">
          Form Tambah Barang Belanja Modal
        </h1>
        <form onSubmit={formik.handleSubmit} className="flex flex-col gap-6">
          {/* Basic Information */}

          <FormHeader formik={formik} />

          <span className=" border-b-2 border-default-400 pb-2 " />
          {/* Dynamic List Obat Section */}
          <FormDinamic
            formik={formik}
            addItem={addObatItem}
            removeItem={removeItem}
          />

          <div className="flex gap-6 justify-end">
            <div className="flex justify-end pt-4">
              <Button
                color="default"
                className="bg-default-50 border-2 border-md"
                size="md"
                onPress={() => {
                  formik.resetForm();
                  route.back();
                }}
              >
                Kembali
              </Button>
            </div>
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                color="primary"
                size="md"
                isDisabled={formik.values.items.length === 0}
              >
                Simpan
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
