"use client";
import { data } from "@/app/obat/inventori-obat/add/page";
import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import { Button, DateValue, SelectItem } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import React from "react";

type payload = {
  title: string;
  typeDrug: string;
  type: string;
  fund: string;
  receipt: string;
  date: DateValue | null;
};

const initialValues: payload = {
  title: "",
  typeDrug: "",
  type: "",
  fund: "",
  receipt: "",
  date: null,
};

export default function FormAddDrug() {
  const route = useRouter();

  const handleSubmit = async (value: payload) => {
    console.log({ value });
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: [],
    onSubmit: handleSubmit,
  });

  return (
    <div className="bg-default-50 rounded-md p-6 flex flex-col gap-6">
      <div className="grid grid-cols-2 lg:grid-cols-3">
        <div className="flex flex-col lg:col-span-1 gap-2 text-sm">
          <p>Nama Obat</p>
          <p>Dosis</p>
          <p>Bentuk</p>
          <p>Satuan</p>
          <p>Jenis</p>
          <p>Total Stok</p>
        </div>
        <div className="flex flex-col gap-2 font-medium text-sm">
          <p>: Example</p>
          <p>: Example</p>
          <p>: Example</p>
          <p>: Example</p>
          <p>: Example</p>
          <p>: Example</p>
        </div>
      </div>{" "}
      <span className=" border-b-2 border-default-400 pb-2 " />
      <div className="flex flex-col gap-6">
        <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
          <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
            <label className="text-sm">Tanggal Perolehan</label>
            <FormDatePicker name={`date`} formik={formik} />
          </div>
          <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
            <label className="text-sm">Lokasi (penyimpanan barang)</label>
            <FormSelect
              showSeachbar={true}
              name={`fund`}
              formik={formik}
              placeholder="Pilih ruangan"
            >
              {data.map((item) => (
                <SelectItem
                  key={item.id}
                  textValue={item.nameObat}
                  // Menyimpan seluruh data item sebagai prop
                >
                  <p>{item.nameObat}</p>
                </SelectItem>
              ))}
            </FormSelect>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-4 grid-cols-1">
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="tglMasuk" className="text-sm">
              Nama Barang
            </label>
            <FormInput
              name={`title`}
              isClearable={false}
              formik={formik}
              placeholder="Ketik nama"
            />
          </div>
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="tglMasuk" className="text-sm">
              Nama Barang
            </label>
            <FormInput
              name={`title`}
              isClearable={false}
              formik={formik}
              placeholder="Ketik nama"
            />
          </div>
        </div>
        <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
          <label className="text-sm">Tanggal Perolehan</label>
          <FormDatePicker name={`date`} formik={formik} />
        </div>
        <div className="flex flex-col gap-1 w-full">
          <label htmlFor="tglMasuk" className="text-sm">
            Nama Barang
          </label>
          <FormInput
            name={`title`}
            isClearable={false}
            formik={formik}
            placeholder="Ketik nama"
          />
        </div>
      </div>
      <div className="flex gap-6 justify-end">
        <div className="flex justify-end pt-4">
          <Button
            color="default"
            className="bg-default-50 border-2 border-md"
            size="md"
            onPress={() => {
              formik.resetForm();
              route.back();
            }}
          >
            Kembali
          </Button>
        </div>
        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            color="primary"
            size="md"
            isDisabled={formik.isSubmitting}
          >
            Simpan
          </Button>
        </div>
      </div>
    </div>
  );
}
