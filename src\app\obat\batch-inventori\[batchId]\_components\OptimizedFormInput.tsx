import React, { useState, useCallback, useEffect } from "react";
import { Input, InputProps } from "@heroui/react";
import { FormikProps, getIn } from "formik";
import { NestedKeyOf } from "@/libs/nested-key";
import { classNames } from "@/constant/constant";

type OptimizedFormInputProps<T> = {
  name: NestedKeyOf<T>;
  formik: FormikProps<T>;
  debounceMs?: number;
  isNumeric?: boolean;
} & Omit<InputProps, "onChange" | "value">;

export function OptimizedFormInput<T>({
  name,
  formik,
  debounceMs = 300,
  isNumeric = false,
  ...props
}: OptimizedFormInputProps<T>) {
  const [localValue, setLocalValue] = useState(() => {
    const formikValue = getIn(formik.values, name);
    return isNumeric
      ? formatNumber(formikValue?.toString() || "")
      : formikValue || "";
  });

  const error = getIn(formik.errors, name);
  const touched = getIn(formik.touched, name);
  const isInValid = !!(touched && error);

  // Debounced update to formik
  useEffect(() => {
    const timer = setTimeout(() => {
      const currentFormikValue = getIn(formik.values, name);
      const valueToSet = isNumeric ? parseNumber(localValue) : localValue;

      // Only update if value actually changed
      if (currentFormikValue !== valueToSet) {
        formik.setFieldValue(name as string, valueToSet);
      }
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [localValue, debounceMs, formik, name, isNumeric]);

  // Update local value when formik value changes externally
  useEffect(() => {
    const formikValue = getIn(formik.values, name);
    const displayValue = isNumeric
      ? formatNumber(formikValue?.toString() || "")
      : formikValue || "";

    if (displayValue !== localValue) {
      setLocalValue(displayValue);
    }
  }, [getIn(formik.values, name), isNumeric]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;

      if (isNumeric) {
        const formattedValue = formatNumber(rawValue);
        setLocalValue(formattedValue);
      } else {
        setLocalValue(rawValue);
      }
    },
    [isNumeric]
  );

  return (
    <Input
      {...props}
      value={localValue}
      onChange={handleChange}
      color={isInValid ? "danger" : "default"}
      errorMessage={error?.toString()}
      isInvalid={isInValid}
      variant="bordered"
      classNames={{ label: classNames.input.label }}
    />
  );
}

// Utility functions
const formatNumber = (num: string) =>
  num.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

const parseNumber = (formattedValue: string) => {
  const cleanValue = formattedValue.replace(/\./g, "");
  return cleanValue === "" ? 0 : Number(cleanValue) || 0;
};
